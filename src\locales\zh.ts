export default {
  lagname: "zh",
  mes: {
    // Common actions
    add: "添加",
    edit: "编辑",
    delete: "删除",
    save: "保存",
    cancel: "取消",
    confirm: "确认",
    search: "搜索",
    refresh: "刷新",
    create: "创建",
    update: "更新",
    close: "关闭",
    back: "返回",
    actions: "操作",
    detail: "详情",
    
    // Navigation
    dashboard: "仪表板",
    products: "产品",
    orders: "订单",
    materials: "原材料",
    bom: "BOM",
    homepage: "首页",
    settings: "设置",
    
    // User
    user: "用户",
    profile: "个人资料",
    logout: "退出登录",
    login: "登录",
    username: "用户名",
    password: "密码",
    systemDescription: "生产管理系统",
    pleaseEnterUsername: "请输入用户名",
    pleaseEnterPassword: "请输入密码",
    
    // Status
    loading: "加载中...",
    success: "成功",
    error: "错误",
    warning: "警告",
    info: "信息",
    pending: "待处理",
    inProgress: "处理中",
    completed: "已完成",
    cancelled: "已取消",
    
    // Table
    noData: "暂无数据",
    total: "总计",
    items: "项",
    page: "页",
    of: "共",
    
    // Form labels
    name: "名称",
    code: "代码",
    description: "描述",
    price: "价格",
    quantity: "数量",
    date: "日期",
    status: "状态",
    color: "颜色",
    unit: "单位",
    supplier: "供应商",
    cost: "成本",
    weight: "重量 (g)",
    injectionTime: "注塑时间 (s)",
    note: "备注",
    unitPrice: "单价",
    totalAmount: "总金额",
    product: "产品",
    
    // Stock management
    addStock: "入库",
    reduceStock: "出库",
    stockQuantity: "库存数量",
    stock: "库存",
    noBOMForStock: "无法入库：产品未定义BOM",
    
    // Messages
    confirmDelete: "确定要删除吗？",
    deleteSuccess: "删除成功",
    saveSuccess: "保存成功",
    updateSuccess: "更新成功",
    createSuccess: "创建成功",
    pleaseSelect: "请选择",
    pleaseEnter: "请输入",
    
    // Page titles
    productManagement: "产品管理",
    orderManagement: "订单管理",
    materialManagement: "原材料管理",
    bomManagement: "BOM管理",
    
    // Actions
    addProduct: "添加产品",
    addOrder: "创建订单",
    addMaterial: "添加原材料",
    addBOM: "添加BOM",
    calculateCost: "计算成本",
    selectProduct: "选择产品",
    
    // Filters
    all: "全部",
    filterByColor: "按颜色筛选",
    filterByStatus: "按状态筛选",
    searchProduct: "搜索产品...",
    searchOrder: "搜索订单...",
    searchMaterial: "搜索材料...",
    
    // Statistics
    totalOrders: "总订单数",
    totalProducts: "产品",
    totalMaterials: "原材料",
    pendingOrders: "待处理订单",
    lowStockAlert: "库存预警",
    recentOrders: "最近订单",
    remaining: "剩余",
    noLowStockAlert: "无库存预警",
    noRecentOrders: "无最近订单",
    topSellingProducts: "热销产品",
    orderStatisticsByMonth: "月度订单统计",
    month: "月",
    
    // Inventory Statistics
    totalInventoryValue: "总库存价值",
    averageQuantity: "平均数量",
    lowStockProducts: "低库存产品",
    outOfStockProducts: "缺货产品",
    lowStockAlerts: "低库存预警",
    noLowStockProducts: "无低库存产品",
    topInventoryProducts: "高库存产品",
    inventoryMovements: "库存变动",
    stockIn: "入库",
    stockOut: "出库",
    stockAdjust: "库存调整",
    movements: "变动",
    
    // Movement History
    movementHistory: "库存移动历史",
    movementHistoryDescription: "跟踪和管理详细的库存移动历史",
    movementType: "移动类型",
    selectMovementType: "选择移动类型",
    dateRange: "日期范围",
    startDate: "开始日期",
    endDate: "结束日期",
    searchByNotes: "按备注搜索",
    enterKeyword: "输入关键词...",
    pleaseEnterKeyword: "请输入关键词",
    orderSummary: "订单汇总",
    charts: "图表",
    movementTypeChart: "移动类型图表",
    movementValueChart: "移动价值图表",
    totalIn: "总入库",
    totalOut: "总出库",
    netMovement: "净移动",
    movementCount: "移动次数",
    lastMovement: "最后移动",
    totalQuantity: "总数量",
    totalValue: "总价值",
    cannotLoadMovementHistory: "无法加载移动历史",
    cannotLoadMovementStats: "无法加载移动统计",
    cannotLoadOrderSummary: "无法加载订单汇总",
    cannotSearchNotes: "无法搜索备注",
    reset: "重置",
    
    // Order specific
    orderCode: "订单号",
    orderDate: "下单日期",
    planDeliveryDate: "计划交付日期",
    actualDeliveryDate: "实际交付日期",
    productCount: "产品数量",
    createdAt: "创建时间",
    orderDetail: "订单详情",
    productList: "产品列表",
    productCode: "产品代码",
    shippedQuantity: "已发货",
    createNewOrder: "创建新订单",
    updateOrder: "更新订单",
    editOrder: "编辑订单",
    deleteOrder: "删除订单",
    selectOrderDate: "选择下单日期",
    selectPlanDeliveryDate: "选择计划交付日期",
    selectActualDeliveryDate: "选择实际交付日期",
    selectStatus: "选择状态",
    currentProductList: "当前产品列表",
    addNewProducts: "添加新产品",
    noNewProducts: "暂无新产品",
    pleaseSelectOrderDate: "请选择下单日期",
    pleaseSelectPlanDeliveryDate: "请选择预计交付日期",
    pleaseSelectStatus: "请选择状态",
    cannotLoadProducts: "无法加载产品列表",
    updateOrderSuccess: "更新订单成功",
    cannotUpdateOrder: "无法更新订单",
    
    // Material specific
    materialCode: "材料代码",
    
    // 404 Page
    pageNotFound: "页面未找到",
    pageNotFoundDescription: "抱歉，您要查找的页面不存在或已被移动。",
    backToHome: "返回首页",
    goBack: "返回",
    helpfulLinks: "有用链接",
    materialName: "材料名称",
    noMaterials: "暂无材料",
    cannotLoadMaterials: "无法加载材料列表",
    addNewMaterial: "添加新材料",
    editMaterial: "编辑材料",
    deleteMaterial: "删除材料",
    pleaseEnterMaterialCode: "请输入材料代码",
    pleaseEnterMaterialName: "请输入材料名称",
    pleaseEnterSupplier: "请输入供应商",
    pleaseEnterUnit: "请输入单位",
    addMaterialSuccess: "添加材料成功",
    cannotAddMaterial: "无法添加材料",
    
    // Material form validation messages
    materialCodeLength: "材料代码必须为2-50个字符",
    materialNameLength: "材料名称必须为2-100个字符",
    supplierLength: "供应商名称必须为2-100个字符",
    unitLength: "单位必须为1-20个字符",
    unitCostMustBePositive: "单价必须大于0",
    
    // Material form placeholders
    enterMaterialCode: "输入材料代码...",
    enterMaterialName: "输入材料名称...",
    enterSupplier: "输入供应商名称...",
    enterUnitCost: "输入单价",
    enterUnit: "输入单位...",
    selectSupplier: "选择供应商",
    newMaterial: "新材料",
    addingMaterial: "添加中...",
    
    // BOM specific
    bomForProduct: "产品BOM",
    costCalculationResult: "成本计算结果",
    totalCost: "总成本",
    addBOMEntries: "添加BOM条目",
    selectMaterial: "选择材料",
    pleaseSelectMaterial: "请选择材料",
    addBOMSuccess: "添加BOM成功",
    cannotAddBOM: "无法添加BOM",

    // Product specific
    productName: "产品名称",
    ProductName: "产品名称",
    addNewProduct: "添加新产品",
    editProduct: "编辑产品",
    deleteProduct: "删除产品",
    pleaseEnterProductName: "请输入产品名称",
    pleaseEnterWeight: "请输入重量",
    pleaseEnterInjectionTime: "请输入注塑时间",
    pleaseEnterColor: "请输入颜色",
    pleaseEnterUnitPrice: "请输入单价",
    pleaseEnterQuantity: "请输入数量",
    addProductSuccess: "添加产品成功",
    cannotAddProduct: "无法添加产品",
    addStockSuccess: "入库成功",
    reduceStockSuccess: "出库成功",
    cannotAddStock: "无法入库",
    cannotReduceStock: "无法出库",
    
    // Form validation messages
    productNameLength: "产品名称必须为2-100个字符",
    weightMustBePositive: "重量必须大于0",
    injectionTimeMustBePositive: "注塑时间必须大于0",
    unitPriceMustBePositive: "单价必须大于0",
    pleaseCheckFormErrors: "请检查表单错误",
    
    // Form placeholders and labels
    enterProductName: "输入产品名称...",
    enterWeight: "输入重量",
    enterInjectionTime: "输入注塑时间",
    enterUnitPrice: "输入单价",
    enterDescription: "输入产品描述...",
    selectColor: "选择颜色",
    additionalInfo: "附加信息",
    newProduct: "新产品",
    adding: "添加中...",
    allFieldsRequired: "所有字段都是必填的",
    
    // Color options
    white: "白色",
    black: "黑色", 
    red: "红色",
    blue: "蓝色",
    green: "绿色",
    yellow: "黄色",
    gray: "灰色",
    
    // Units
    seconds: "秒",
    
    // Form state messages
    unsavedChanges: "未保存的更改",
    unsavedChangesMessage: "您有未保存的更改。确定要丢弃它们吗？",
    keepEditing: "继续编辑",
    discardChanges: "丢弃更改",
    restorePreviousData: "您有之前保存的数据。是否要恢复？",
    savedDataFound: "找到保存的数据",
    restore: "恢复",
    startFresh: "重新开始",

    // Dashboard
    welcome: "欢迎使用ERP系统",
    recentProducts: "最近产品",
    recentMaterials: "最近材料",
    viewAll: "查看全部",

    // Language
    language: "语言",
    vietnamese: "Tiếng Việt",
    chinese: "中文",
    english: "English",

    // Currency
    currency: "货币",
    vnd: "越南盾",
    cny: "人民币",
    usd: "美元",
    currencySymbol: "¥",
    currencyFormat: "CNY",

    // Additional keys
    loadProductError: '无法加载产品列表',
    loadMaterialError: '无法加载物料列表',
    loadBOMError: '无法加载BOM',
    calculateCostError: '无法计算成本',
    calculateCostSuccess: '成本计算成功',
    quantityMustBePositive: '数量必须大于0',
    pleaseSelectOrderStatus: '请选择订单状态',
    loadOrderError: '无法加载订单列表',
    confirmDeleteProduct: '您确定要删除产品"{name}"吗？',
    deleteProductSuccess: '产品删除成功',
    cannotDeleteProduct: '无法删除产品',
    updateBOM: '更新BOM',
    updateBOMSuccess: 'BOM更新成功',
    cannotUpdateBOM: '无法更新BOM',
    updateQuantity: '更新数量',
    updateQuantitySuccess: '物料数量更新成功',
    cannotUpdateQuantity: '无法更新物料数量',
    selectOrder: "选择订单"
  },
}; 