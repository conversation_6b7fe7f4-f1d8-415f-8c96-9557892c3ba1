import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
const urlIp = `${import.meta.env.VITE_BACKEND_URL}api/v1`;
const api = axios.create({
  baseURL: urlIp,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const message = error.response?.data?.message || error.message || 'Có lỗi xảy ra'
    
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
      ElMessage.error('<PERSON><PERSON><PERSON> đăng nhập đã hết hạn')
    } else {
      ElMessage.error(message)
    }
    
    return Promise.reject(error)
  }
)

export default api 