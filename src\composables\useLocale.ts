import { computed, ComputedRef } from 'vue'
import { useI18n } from 'vue-i18n'

export function useLocale(): {
  currentLocale: ComputedRef<string>
  formatDateByLocale: (dateString: string) => string
  formatDateTimeByLocale: (dateString: string) => string
  getLocaleCode: () => string
  getCurrencySymbol: () => string
  formatCurrency: (amount: number) => string
  getCurrencyCode: () => string
} {
  const { locale } = useI18n()

  const currentLocale = computed((): string => locale.value)

  function formatDateByLocale(dateString: string): string {
    if (!dateString) return ''
    
    const date = new Date(dateString)
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }
    
    return date.toLocaleDateString(getLocaleCode(), options)
  }

  function formatDateTimeByLocale(dateString: string): string {
    if (!dateString) return ''
    
    const date = new Date(dateString)
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }
    
    return date.toLocaleDateString(getLocaleCode(), options)
  }

  function getLocaleCode(): string {
    switch (currentLocale.value) {
      case 'vi':
        return 'vi-VN'
      case 'zh':
        return 'zh-CN'
      case 'en':
      default:
        return 'en-US'
    }
  }

  function getCurrencySymbol(): string {
    switch (currentLocale.value) {
      case 'vi':
        return '₫'
      case 'zh':
        return '¥'
      case 'en':
      default:
        return '$'
    }
  }

  function formatCurrency(amount: number): string {
    const formatter = new Intl.NumberFormat(getLocaleCode(), {
      style: 'currency',
      currency: getCurrencyCode()
    })
    return formatter.format(amount)
  }

  function getCurrencyCode(): string {
    switch (currentLocale.value) {
      case 'vi':
        return 'VND'
      case 'zh':
        return 'CNY'
      case 'en':
      default:
        return 'USD'
    }
  }

  return {
    currentLocale,
    formatDateByLocale,
    formatDateTimeByLocale,
    getLocaleCode,
    getCurrencySymbol,
    formatCurrency,
    getCurrencyCode
  }
} 