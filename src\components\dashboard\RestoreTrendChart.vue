<template>
  <div class="chart-container bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Restore Trends (Last 30 Days)</h3>
      <div class="flex items-center space-x-4">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Successful</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">Failed</span>
        </div>
      </div>
    </div>
    <div ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { ChartDataPoint } from '@/types/database.types'
import { format, parseISO } from 'date-fns'

interface Props {
  data: ChartDataPoint[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

function initChart() {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

function updateChart() {
  if (!chartInstance || !props.data.length) return

  const dates = props.data.map(item => format(parseISO(item.date), 'MM/dd'))
  const successData = props.data.map(item => item.success)
  const failedData = props.data.map(item => item.failed)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function(params: any) {
        const date = format(parseISO(props.data[params[0].dataIndex].date), 'MMM dd, yyyy')
        let tooltip = `<div class="font-semibold">${date}</div>`
        params.forEach((param: any) => {
          tooltip += `<div class="flex items-center mt-1">
            <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${param.color}"></div>
            <span>${param.seriesName}: ${param.value}</span>
          </div>`
        })
        return tooltip
      }
    },
    legend: {
      data: ['Successful', 'Failed'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1
    },
    series: [
      {
        name: 'Successful',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(34, 197, 94, 0.3)'
            }, {
              offset: 1, color: 'rgba(34, 197, 94, 0.1)'
            }]
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: successData,
        itemStyle: {
          color: '#22c55e'
        },
        lineStyle: {
          color: '#22c55e'
        }
      },
      {
        name: 'Failed',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(239, 68, 68, 0.3)'
            }, {
              offset: 1, color: 'rgba(239, 68, 68, 0.1)'
            }]
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: failedData,
        itemStyle: {
          color: '#ef4444'
        },
        lineStyle: {
          color: '#ef4444'
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

function resizeChart() {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// Watch for data changes
watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// Watch for loading state
watch(() => props.loading, (loading) => {
  if (chartInstance) {
    if (loading) {
      chartInstance.showLoading()
    } else {
      chartInstance.hideLoading()
    }
  }
})

onMounted(() => {
  initChart()
  window.addEventListener('resize', resizeChart)
})

// Cleanup
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
.chart-container {
  min-height: 400px;
}
</style>
