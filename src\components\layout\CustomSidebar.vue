<template>
  <el-aside :width="sidebarCollapsed ? '64px' : '200px'" class="sidebar">
    <div class="logo-container">
      <img src="@/assets/logo.ico" alt="Logo" class="logo" ref="logoRef" />
      <span v-show="!sidebarCollapsed" class="logo-text" ref="logoTextRef"
        >IT Supreme Leader</span
      >
    </div>

    <el-menu
      :default-active="$route.path"
      :collapse="sidebarCollapsed"
      router
      class="sidebar-menu"
      background-color="transparent"
      text-color="var(--el-text-color-primary)"
      active-text-color="var(--el-color-primary)"
      ref="menuRef"
    >
      <el-menu-item index="/dashboard" ref="menuItem1">
        <el-icon><DataBoard /></el-icon>
        <span>{{ t("mes.dashboard") }}</span>
      </el-menu-item>

      <el-menu-item index="/products" ref="menuItem2">
        <el-icon><Goods /></el-icon>
        <span>{{ t("mes.products") }}</span>
      </el-menu-item>
    </el-menu>
  </el-aside>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/stores/app";
import { DataBoard, Goods } from "@element-plus/icons-vue";
import gsap from "gsap";

const { t } = useI18n();
const appStore = useAppStore();
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed);

// Refs for animation targets
const logoRef = ref<HTMLElement | null>(null);
const logoTextRef = ref<HTMLElement | null>(null);
const menuRef = ref<HTMLElement | null>(null);
const menuItem1 = ref<HTMLElement | null>(null);
const menuItem2 = ref<HTMLElement | null>(null);

// Watch for sidebar state changes
watch(sidebarCollapsed, (collapsed) => {
  // Animate logo
  gsap.to(logoRef.value, {
    scale: collapsed ? 0.8 : 1,
    duration: 0.3,
    ease: "power2.out",
  });

  // Animate logo text
  if (logoTextRef.value) {
    gsap.to(logoTextRef.value, {
      opacity: collapsed ? 0 : 1,
      duration: 0.2,
      ease: "power2.inOut",
    });
  }

  // Animate menu items
  const menuItems = [menuItem1.value, menuItem2.value];
  menuItems.forEach((item, index) => {
    if (item) {
      gsap.from(item, {
        x: collapsed ? -20 : 0,
        opacity: collapsed ? 0 : 1,
        duration: 0.3,
        delay: index * 0.05,
        ease: "power2.out",
      });
    }
  });
});
</script>

<style scoped>
.sidebar {
  background-color: var(--el-bg-color-overlay);
  border-right: 1px solid var(--el-border-color);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-x: hidden;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  border-bottom: 1px solid var(--el-border-color);
  overflow: hidden;
}

.logo {
  height: 32px;
  width: 32px;
  margin-right: 12px;
  object-fit: contain;
  will-change: transform;
}

.logo-text {
  font-size: 15px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  will-change: opacity;
}

.sidebar-menu {
  border-right: none;
}

.el-menu-item {
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-menu-item.is-active {
  background-color: var(--el-color-primary-light-9);
}

.el-menu-item:hover {
  transform: translateX(4px);
}
</style>
