export default {
  lagname: "en",
  mes: {
    // Common actions
    add: "Add",
    edit: "Edit",
    delete: "Delete",
    save: "Save",
    cancel: "Cancel",
    confirm: "Confirm",
    search: "Search",
    refresh: "Refresh",
    create: "Create",
    update: "Update",
    close: "Close",
    back: "Back",
    actions: "Actions",
    detail: "Detail",
    
    // Navigation
    dashboard: "Dashboard",
    products: "Products",
    orders: "Orders",
    materials: "Materials",
    bom: "BOM",
    homepage: "Home",
    settings: "Settings",
    
    // User
    user: "User",
    profile: "Profile",
    logout: "Logout",
    login: "Login",
    username: "Userna<PERSON>",
    password: "Password",
    systemDescription: "Production Management System",
    pleaseEnterUsername: "Please enter username",
    pleaseEnterPassword: "Please enter password",
    
    // Status
    loading: "Loading...",
    success: "Success",
    error: "Error",
    warning: "Warning",
    info: "Information",
    pending: "Pending",
    inProgress: "In Progress",
    completed: "Completed",
    cancelled: "Cancelled",
    
    // Table
    noData: "No data",
    total: "Total",
    items: "items",
    page: "Page",
    of: "of",
    
    // Form labels
    name: "Name",
    code: "Code",
    description: "Description",
    price: "Price",
    quantity: "Quantity",
    date: "Date",
    status: "Status",
    color: "Color",
    unit: "Unit",
    supplier: "Supplier",
    cost: "Cost",
    weight: "Weight (g)",
    injectionTime: "Injection Time (s)",
    note: "Note",
    unitPrice: "Unit Price",
    totalAmount: "Total Amount",
    product: "Product",
    
    // Stock management
    addStock: "Add Stock",
    reduceStock: "Reduce Stock",
    stockQuantity: "Stock Quantity",
    stock: "Stock",
    noBOMForStock: "Cannot add stock: Product has no BOM defined",
    
    // Messages
    confirmDelete: "Are you sure you want to delete?",
    deleteSuccess: "Delete successful",
    saveSuccess: "Save successful",
    updateSuccess: "Update successful",
    createSuccess: "Create successful",
    pleaseSelect: "Please select",
    pleaseEnter: "Please enter",
    
    // Page titles
    productManagement: "Product Management",
    orderManagement: "Order Management",
    materialManagement: "Material Management",
    bomManagement: "BOM Management",
    
    // Actions
    addProduct: "Add Product",
    addOrder: "Create Order",
    addMaterial: "Add Material",
    addBOM: "Add BOM",
    calculateCost: "Calculate Cost",
    selectProduct: "Select Product",
    
    // Filters
    all: "All",
    filterByColor: "Filter by Color",
    filterByStatus: "Filter by Status",
    searchProduct: "Search products...",
    searchOrder: "Search orders...",
    searchMaterial: "Search materials...",
    
    // Statistics
    totalOrders: "Total Orders",
    totalProducts: "Products",
    totalMaterials: "Materials",
    pendingOrders: "Pending Orders",
    lowStockAlert: "Low Stock Alert",
    recentOrders: "Recent Orders",
    remaining: "Remaining",
    noLowStockAlert: "No low stock alerts",
    noRecentOrders: "No recent orders",
    topSellingProducts: "Top Selling Products",
    orderStatisticsByMonth: "Monthly Order Statistics",
    month: "Month",
    
    // Inventory Statistics
    totalInventoryValue: "Total Inventory Value",
    averageQuantity: "Average Quantity",
    lowStockProducts: "Low Stock Products",
    outOfStockProducts: "Out of Stock Products",
    lowStockAlerts: "Low Stock Alerts",
    noLowStockProducts: "No low stock products",
    topInventoryProducts: "Top Inventory Products",
    inventoryMovements: "Inventory Movements",
    stockIn: "Stock In",
    stockOut: "Stock Out",
    stockAdjust: "Stock Adjust",
    movements: "Movements",
    
    // Movement History
    movementHistory: "Movement History",
    movementHistoryDescription: "Track and manage detailed inventory movement history",
    movementType: "Movement Type",
    selectMovementType: "Select movement type",
    dateRange: "Date range",
    startDate: "Start date",
    endDate: "End date",
    searchByNotes: "Search by notes",
    enterKeyword: "Enter keyword...",
    pleaseEnterKeyword: "Please enter keyword",
    orderSummary: "Order Summary",
    charts: "Charts",
    movementTypeChart: "Movement Type Chart",
    movementValueChart: "Movement Value Chart",
    totalIn: "Total In",
    totalOut: "Total Out",
    netMovement: "Net Movement",
    movementCount: "Movement Count",
    lastMovement: "Last Movement",
    totalQuantity: "Total Quantity",
    totalValue: "Total Value",
    cannotLoadMovementHistory: "Cannot load movement history",
    cannotLoadMovementStats: "Cannot load movement statistics",
    cannotLoadOrderSummary: "Cannot load order summary",
    cannotSearchNotes: "Cannot search notes",
    reset: "Reset",
    
    // Order specific
    orderCode: "Order Code",
    orderDate: "Order Date",
    planDeliveryDate: "Planned Delivery Date",
    actualDeliveryDate: "Actual Delivery Date",
    productCount: "Product Count",
    createdAt: "Created At",
    orderDetail: "Order Detail",
    productList: "Product List",
    productCode: "Product Code",
    shippedQuantity: "Shipped Quantity",
    createNewOrder: "Create New Order",
    updateOrder: "Update Order",
    editOrder: "Edit Order",
    deleteOrder: "Delete Order",
    selectOrderDate: "Select Order Date",
    selectPlanDeliveryDate: "Select Planned Delivery Date",
    selectActualDeliveryDate: "Select Actual Delivery Date",
    selectStatus: "Select Status",
    currentProductList: "Current Product List",
    addNewProducts: "Add New Products",
    noNewProducts: "No new products",
    pleaseSelectOrderDate: "Please select order date",
    pleaseSelectPlanDeliveryDate: "Please select planned delivery date",
    pleaseSelectStatus: "Please select status",
    cannotLoadProducts: "Cannot load product list",
    updateOrderSuccess: "Update order successful",
    cannotUpdateOrder: "Cannot update order",
    
    // Material specific
    materialCode: "Material Code",
    
    // 404 Page
    pageNotFound: "Page Not Found",
    pageNotFoundDescription: "Sorry, the page you are looking for does not exist or has been moved.",
    backToHome: "Back to Home",
    goBack: "Go Back",
    helpfulLinks: "Helpful Links",
    materialName: "Material Name",
    noMaterials: "No materials",
    cannotLoadMaterials: "Cannot load material list",
    addNewMaterial: "Add New Material",
    editMaterial: "Edit Material",
    deleteMaterial: "Delete Material",
    pleaseEnterMaterialCode: "Please enter material code",
    pleaseEnterMaterialName: "Please enter material name",
    pleaseEnterSupplier: "Please enter supplier",
    pleaseEnterUnit: "Please enter unit",
    addMaterialSuccess: "Add material successful",
    cannotAddMaterial: "Cannot add material",
    
    // Material form validation messages
    materialCodeLength: "Material code must be 2-50 characters",
    materialNameLength: "Material name must be 2-100 characters",
    supplierLength: "Supplier name must be 2-100 characters",
    unitLength: "Unit must be 1-20 characters",
    unitCostMustBePositive: "Unit cost must be greater than 0",
    
    // Material form placeholders
    enterMaterialCode: "Enter material code...",
    enterMaterialName: "Enter material name...",
    enterSupplier: "Enter supplier name...",
    enterUnitCost: "Enter unit cost",
    enterUnit: "Enter unit...",
    selectSupplier: "Select supplier",
    newMaterial: "New Material",
    addingMaterial: "Adding...",
    
    // BOM specific
    bomForProduct: "BOM for Product",
    costCalculationResult: "Cost Calculation Result",
    totalCost: "Total Cost",
    addBOMEntries: "Add BOM Entries",
    selectMaterial: "Select Material",
    pleaseSelectMaterial: "Please select material",
    addBOMSuccess: "Add BOM successful",
    cannotAddBOM: "Cannot add BOM",

    // Product specific
    productName: "Product Name",
    ProductName: "Product Name",
    addNewProduct: "Add New Product",
    editProduct: "Edit Product",
    deleteProduct: "Delete Product",
    pleaseEnterProductName: "Please enter product name",
    pleaseEnterWeight: "Please enter weight",
    pleaseEnterInjectionTime: "Please enter injection time",
    pleaseEnterColor: "Please enter color",
    pleaseEnterUnitPrice: "Please enter unit price",
    pleaseEnterQuantity: "Please enter quantity",
    addProductSuccess: "Add product successful",
    cannotAddProduct: "Cannot add product",
    addStockSuccess: "Add stock successful",
    reduceStockSuccess: "Reduce stock successful",
    cannotAddStock: "Cannot add stock",
    cannotReduceStock: "Cannot reduce stock",
    
    // Form validation messages
    productNameLength: "Product name must be 2-100 characters",
    weightMustBePositive: "Weight must be greater than 0",
    injectionTimeMustBePositive: "Injection time must be greater than 0",
    unitPriceMustBePositive: "Unit price must be greater than 0",
    pleaseCheckFormErrors: "Please check form errors",
    
    // Form placeholders and labels
    enterProductName: "Enter product name...",
    enterWeight: "Enter weight",
    enterInjectionTime: "Enter injection time",
    enterUnitPrice: "Enter unit price",
    enterDescription: "Enter product description...",
    selectColor: "Select color",
    additionalInfo: "Additional Information",
    newProduct: "New Product",
    adding: "Adding...",
    allFieldsRequired: "All fields are required",
    
    // Color options
    white: "White",
    black: "Black", 
    red: "Red",
    blue: "Blue",
    green: "Green",
    yellow: "Yellow",
    gray: "Gray",
    
    // Units
    seconds: "seconds",
    
    // Form state messages
    unsavedChanges: "Unsaved Changes",
    unsavedChangesMessage: "You have unsaved changes. Are you sure you want to discard them?",
    keepEditing: "Keep Editing",
    discardChanges: "Discard Changes",
    restorePreviousData: "You have saved data from before. Would you like to restore it?",
    savedDataFound: "Saved Data Found",
    restore: "Restore",
    startFresh: "Start Fresh",

    // Dashboard
    welcome: "Welcome to ERP System",
    recentProducts: "Recent Products",
    recentMaterials: "Recent Materials",
    viewAll: "View All",

    // Language
    language: "Language",
    vietnamese: "Tiếng Việt",
    chinese: "中文",
    english: "English",

    // Currency
    currency: "Currency",
    vnd: "Vietnamese Dong",
    cny: "Chinese Yuan",
    usd: "US Dollar",
    currencySymbol: "$",
    currencyFormat: "USD",
    loadProductError: 'Failed to load product list',
    loadMaterialError: 'Failed to load material list',
    loadBOMError: 'Failed to load BOM',
    calculateCostError: 'Failed to calculate cost',
    calculateCostSuccess: 'Cost calculated successfully',
    quantityMustBePositive: 'Quantity must be greater than 0',
    pleaseSelectOrderStatus: 'Please select order status',
    loadOrderError: 'Failed to load order list',
    confirmDeleteProduct: 'Are you sure you want to delete product "{name}"?',
    deleteProductSuccess: 'Product deleted successfully',
    cannotDeleteProduct: 'Cannot delete product',
    updateBOM: 'Update BOM',
    updateBOMSuccess: 'BOM updated successfully',
    cannotUpdateBOM: 'Cannot update BOM',
    updateQuantity: 'Update Quantity',
    updateQuantitySuccess: 'Material quantity updated successfully',
    cannotUpdateQuantity: 'Cannot update material quantity',
    selectOrder: "Select Order"
  }
} 