import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { dashboardService, restoreDatabaseService, setupRestoreService } from '@/api/database'
import type { 
  ToolRestoreSQLDatabase, 
  SetupToolRestoreSQL, 
  DashboardStats,
  ChartDataPoint,
  DatabaseSizeData,
  ActivityLog
} from '@/types/database.types'
import { format, parseISO, subDays, isWithinInterval } from 'date-fns'

export function useDashboard() {
  // Reactive state
  const loading = ref(false)
  const error = ref<string | null>(null)
  const restoreList = ref<ToolRestoreSQLDatabase[]>([])
  const setupList = ref<SetupToolRestoreSQL[]>([])
  const serverStatus = ref(false)
  const dbStatus = ref(false)

  // Computed statistics
  const dashboardStats = computed<DashboardStats>(() => {
    const totalRestores = restoreList.value.length
    const successfulRestores = restoreList.value.filter(item => item.IsSuccess === '1').length
    const failedRestores = totalRestores - successfulRestores
    const totalSetups = setupList.value.length
    const autoSetups = setupList.value.filter(item => item.IsAuto === '1').length
    
    // Recent activity (last 7 days)
    const sevenDaysAgo = subDays(new Date(), 7)
    const recentActivity = restoreList.value.filter(item => {
      if (!item.DateTimeRestore) return false
      try {
        const itemDate = parseISO(item.DateTimeRestore)
        return isWithinInterval(itemDate, { start: sevenDaysAgo, end: new Date() })
      } catch {
        return false
      }
    }).length

    return {
      totalRestores,
      successfulRestores,
      failedRestores,
      totalSetups,
      autoSetups,
      recentActivity
    }
  })

  // Chart data for restore trends
  const chartData = computed<ChartDataPoint[]>(() => {
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = subDays(new Date(), 29 - i)
      return format(date, 'yyyy-MM-dd')
    })

    return last30Days.map(date => {
      const dayRestores = restoreList.value.filter(item => item.DateTimeRestore === date)
      const success = dayRestores.filter(item => item.IsSuccess === '1').length
      const failed = dayRestores.filter(item => item.IsSuccess === '0').length

      return {
        date,
        success,
        failed
      }
    })
  })

  // Database size data for charts
  const databaseSizeData = computed<DatabaseSizeData[]>(() => {
    const sizeMap = new Map<string, { zipTotal: number, bakTotal: number, count: number }>()

    restoreList.value.forEach(item => {
      const key = item.DatabaseName
      const existing = sizeMap.get(key) || { zipTotal: 0, bakTotal: 0, count: 0 }
      
      // Parse size strings (assuming format like "100MB", "1.5GB")
      const zipSize = parseSizeString(item.SizeFileZip)
      const bakSize = parseSizeString(item.SizeBAK)
      
      sizeMap.set(key, {
        zipTotal: existing.zipTotal + zipSize,
        bakTotal: existing.bakTotal + bakSize,
        count: existing.count + 1
      })
    })

    return Array.from(sizeMap.entries()).map(([name, data]) => ({
      name,
      zipSize: Math.round(data.zipTotal / data.count), // Average size
      bakSize: Math.round(data.bakTotal / data.count)
    })).slice(0, 10) // Top 10 databases
  })

  // Recent activity logs
  const activityLogs = computed<ActivityLog[]>(() => {
    const logs: ActivityLog[] = []

    // Add restore activities
    restoreList.value
      .filter(item => item.DateTimeRestore)
      .sort((a, b) => new Date(b.DateTimeRestore!).getTime() - new Date(a.DateTimeRestore!).getTime())
      .slice(0, 10)
      .forEach(item => {
        logs.push({
          id: item.ID || '',
          type: 'restore',
          action: 'Database Restore',
          ip: item.IP,
          databaseName: item.DatabaseName,
          status: item.IsSuccess === '1' ? 'success' : 'failed',
          timestamp: item.DateTimeRestore!,
          details: item.LogContent
        })
      })

    // Add setup activities
    setupList.value
      .filter(item => item.DateTimeRestore)
      .sort((a, b) => new Date(b.DateTimeRestore!).getTime() - new Date(a.DateTimeRestore!).getTime())
      .slice(0, 5)
      .forEach(item => {
        logs.push({
          id: item.ID || '',
          type: 'setup',
          action: 'Setup Configuration',
          ip: item.IP,
          databaseName: item.DatabaseName,
          status: 'success',
          timestamp: item.DateTimeRestore!,
          details: `Auto: ${item.IsAuto === '1' ? 'Yes' : 'No'}`
        })
      })

    return logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 15)
  })

  // Helper function to parse size strings
  function parseSizeString(sizeStr: string): number {
    const match = sizeStr.match(/^([\d.]+)\s*(MB|GB|KB)?$/i)
    if (!match) return 0

    const value = parseFloat(match[1])
    const unit = (match[2] || 'MB').toUpperCase()

    switch (unit) {
      case 'KB': return value / 1024
      case 'MB': return value
      case 'GB': return value * 1024
      default: return value
    }
  }

  // Load dashboard data
  async function loadDashboardData() {
    loading.value = true
    error.value = null

    try {
      const data = await dashboardService.getDashboardData()
      
      restoreList.value = data.restoreList
      setupList.value = data.setupList
      serverStatus.value = data.serverStatus
      dbStatus.value = data.dbStatus

    } catch (err: any) {
      error.value = err.message || 'Failed to load dashboard data'
      ElMessage.error(error.value)
    } finally {
      loading.value = false
    }
  }

  // Refresh data
  async function refreshData() {
    await loadDashboardData()
    ElMessage.success('Dashboard data refreshed')
  }

  // Load data on mount
  onMounted(() => {
    loadDashboardData()
  })

  return {
    // State
    loading,
    error,
    restoreList,
    setupList,
    serverStatus,
    dbStatus,
    
    // Computed
    dashboardStats,
    chartData,
    databaseSizeData,
    activityLogs,
    
    // Methods
    loadDashboardData,
    refreshData
  }
}
