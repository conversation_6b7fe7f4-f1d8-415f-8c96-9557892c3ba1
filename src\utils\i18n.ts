import { useI18n } from 'vue-i18n'
import { ref } from 'vue'

// Available languages
type LocaleOption = {
  code: string
  name: string
  flag: string
}

export const availableLocales: LocaleOption[] = [
  { code: 'vi', name: 'Tiếng Việt', flag: '🇻🇳' },
  { code: 'zh', name: '中文', flag: '🇨🇳' }
]

// Current locale reactive reference
export const currentLocale = ref<string>('vi')

// Language switching function
export function useLanguageSwitcher(): {
  switchLanguage: (langCode: string) => void
  getCurrentLocale: () => string
  initializeLocale: () => void
  availableLocales: LocaleOption[]
} {
  const { locale } = useI18n()
  
  function switchLanguage(langCode: string): void {
    locale.value = langCode
    currentLocale.value = langCode
    // Store in localStorage for persistence
    localStorage.setItem('locale', langCode)
  }
  
  function getCurrentLocale(): string {
    return currentLocale.value
  }
  
  function initializeLocale(): void {
    // Get stored locale or browser locale
    const storedLocale = localStorage.getItem('locale')
    if (storedLocale && availableLocales.some(lang => lang.code === storedLocale)) {
      switchLanguage(storedLocale)
    } else {
      // Use browser language detection
      const browserLang = navigator.language.toLowerCase()
      if (browserLang.startsWith('zh')) {
        switchLanguage('zh')
      } else {
        switchLanguage('vi') // Default to Vietnamese
      }
    }
  }
  
  return {
    switchLanguage,
    getCurrentLocale,
    initializeLocale,
    availableLocales
  }
}

// Translation helper with fallback
export function useTranslation(): {
  t: (key: string, fallback?: string) => string
  te: (key: string) => boolean
} {
  const { t, te } = useI18n()
  
  function translate(key: string, fallback?: string): string {
    if (te(key)) {
      return t(key)
    }
    return fallback || key
  }
  
  return {
    t: translate,
    te
  }
} 