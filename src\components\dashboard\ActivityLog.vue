<template>
  <div class="activity-log bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
      <el-button 
        type="primary" 
        size="small" 
        :icon="Refresh" 
        @click="$emit('refresh')"
        :loading="loading"
      >
        Refresh
      </el-button>
    </div>

    <div v-if="loading" class="space-y-4">
      <div v-for="i in 5" :key="i" class="animate-pulse">
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="activities.length === 0" class="text-center py-8">
      <el-icon :size="48" class="text-gray-400 mb-4">
        <Document />
      </el-icon>
      <p class="text-gray-500">No recent activity</p>
    </div>

    <div v-else class="space-y-4 max-h-96 overflow-y-auto">
      <div 
        v-for="activity in activities" 
        :key="activity.id"
        class="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
      >
        <div class="flex-shrink-0">
          <div :class="[
            'w-8 h-8 rounded-full flex items-center justify-center',
            getStatusColor(activity.status).bg
          ]">
            <el-icon :size="16" :class="getStatusColor(activity.status).text">
              <component :is="getStatusIcon(activity)" />
            </el-icon>
          </div>
        </div>
        
        <div class="flex-1 min-w-0">
          <div class="flex items-center justify-between">
            <p class="text-sm font-medium text-gray-900 truncate">
              {{ activity.action }}
            </p>
            <p class="text-xs text-gray-500 flex-shrink-0 ml-2">
              {{ formatTime(activity.timestamp) }}
            </p>
          </div>
          
          <div class="mt-1 flex items-center space-x-2 text-sm text-gray-600">
            <span class="inline-flex items-center">
              <el-icon :size="14" class="mr-1">
                <Monitor />
              </el-icon>
              {{ activity.ip }}
            </span>
            <span class="text-gray-400">•</span>
            <span class="inline-flex items-center">
              <el-icon :size="14" class="mr-1">
                <Database />
              </el-icon>
              {{ activity.databaseName }}
            </span>
          </div>
          
          <div v-if="activity.details" class="mt-1">
            <p class="text-xs text-gray-500 truncate">{{ activity.details }}</p>
          </div>
          
          <div class="mt-2">
            <span :class="[
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
              getStatusColor(activity.status).badge
            ]">
              {{ getStatusText(activity.status) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Refresh, 
  Document, 
  Monitor, 
  Database,
  Check,
  Close,
  Clock,
  Setting,
  Refresh as RefreshIcon
} from '@element-plus/icons-vue'
import type { ActivityLog } from '@/types/database.types'
import { format, parseISO, formatDistanceToNow } from 'date-fns'

interface Props {
  activities: ActivityLog[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

defineEmits<{
  refresh: []
}>()

function getStatusIcon(activity: ActivityLog) {
  if (activity.type === 'setup') return Setting
  
  switch (activity.status) {
    case 'success': return Check
    case 'failed': return Close
    case 'pending': return Clock
    default: return RefreshIcon
  }
}

function getStatusColor(status: string) {
  switch (status) {
    case 'success':
      return {
        bg: 'bg-green-100',
        text: 'text-green-600',
        badge: 'bg-green-100 text-green-800'
      }
    case 'failed':
      return {
        bg: 'bg-red-100',
        text: 'text-red-600',
        badge: 'bg-red-100 text-red-800'
      }
    case 'pending':
      return {
        bg: 'bg-yellow-100',
        text: 'text-yellow-600',
        badge: 'bg-yellow-100 text-yellow-800'
      }
    default:
      return {
        bg: 'bg-gray-100',
        text: 'text-gray-600',
        badge: 'bg-gray-100 text-gray-800'
      }
  }
}

function getStatusText(status: string) {
  switch (status) {
    case 'success': return 'Success'
    case 'failed': return 'Failed'
    case 'pending': return 'Pending'
    default: return 'Unknown'
  }
}

function formatTime(timestamp: string) {
  try {
    const date = parseISO(timestamp)
    return formatDistanceToNow(date, { addSuffix: true })
  } catch {
    return timestamp
  }
}
</script>

<style scoped>
.activity-log {
  min-height: 400px;
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
