import { createApp, watch } from "vue";
import { createPinia } from "pinia";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import "element-plus/theme-chalk/dark/css-vars.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import router from "./router";
import i18n from "./i18n";
import App from "./App.vue";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import vi from "element-plus/es/locale/lang/vi";
import en from "element-plus/es/locale/lang/en";
import "./assets/main.css";
const app = createApp(App);

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

const elementLocales = { vi, zh: zhCn, en };

app.use(createPinia());
app.use(router);
app.use(i18n);
app.use(ElementPlus, {
  locale: elementLocales[i18n.global.locale.value] || vi,
});

// Watch for language changes and update Element Plus locale
watch(
  () => i18n.global.locale.value,
  (newLocale) => {
    app.use(ElementPlus, {
      locale: elementLocales[newLocale] || vi,
    });
  },
);

app.mount("#app");
