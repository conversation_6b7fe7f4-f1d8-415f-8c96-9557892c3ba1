<template>
  <div class="dashboard-page min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Database Management Dashboard</h1>
          <p class="text-sm text-gray-600 mt-1">Monitor and manage SQL database restore operations</p>
        </div>
        <div class="flex items-center space-x-3">
          <el-button
            type="primary"
            :icon="Refresh"
            @click="refreshData"
            :loading="loading"
          >
            Refresh All
          </el-button>
          <el-button
            type="default"
            :icon="Setting"
            @click="showSettings = true"
          >
            Settings
          </el-button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="p-6">
      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatsCard
          title="Total Restores"
          :value="dashboardStats.totalRestores"
          :icon="Database"
          color="blue"
          :subtitle="`${dashboardStats.recentActivity} in last 7 days`"
        />
        <StatsCard
          title="Successful Restores"
          :value="dashboardStats.successfulRestores"
          :icon="Check"
          color="green"
          :subtitle="`${successRate}% success rate`"
        />
        <StatsCard
          title="Failed Restores"
          :value="dashboardStats.failedRestores"
          :icon="Close"
          color="red"
          :subtitle="dashboardStats.failedRestores > 0 ? 'Requires attention' : 'All good'"
        />
        <StatsCard
          title="Active Setups"
          :value="dashboardStats.totalSetups"
          :icon="Setting"
          color="purple"
          :subtitle="`${dashboardStats.autoSetups} automated`"
        />
      </div>

      <!-- Charts and Status Row -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Restore Trend Chart -->
        <div class="lg:col-span-2">
          <RestoreTrendChart
            :data="chartData"
            :loading="loading"
          />
        </div>

        <!-- System Status -->
        <div class="lg:col-span-1">
          <SystemStatus
            :server-status="serverStatus"
            :db-status="dbStatus"
            :loading="loading"
            :last-check-time="lastCheckTime"
            @refresh="checkSystemHealth"
          />
        </div>
      </div>

      <!-- Activity and Quick Actions Row -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Activity -->
        <ActivityLog
          :activities="activityLogs"
          :loading="loading"
          @refresh="refreshData"
        />

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div class="space-y-3">
            <el-button
              type="primary"
              class="w-full justify-start"
              :icon="Plus"
              @click="showNewRestoreDialog = true"
            >
              New Database Restore
            </el-button>
            <el-button
              type="default"
              class="w-full justify-start"
              :icon="Setting"
              @click="showNewSetupDialog = true"
            >
              Configure Restore Setup
            </el-button>
            <el-button
              type="default"
              class="w-full justify-start"
              :icon="Document"
              @click="navigateToLogs"
            >
              View All Logs
            </el-button>
            <el-button
              type="default"
              class="w-full justify-start"
              :icon="Monitor"
              @click="navigateToMonitoring"
            >
              System Monitoring
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div v-if="loading && !dashboardStats.totalRestores" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <el-icon class="animate-spin" :size="20">
          <Loading />
        </el-icon>
        <span class="text-gray-700">Loading dashboard data...</span>
      </div>
    </div>

    <!-- Error Alert -->
    <el-alert
      v-if="error"
      :title="error"
      type="error"
      show-icon
      closable
      @close="error = null"
      class="fixed top-4 right-4 z-50 max-w-md"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Setting,
  Coin as Database,
  Check,
  Close,
  Plus,
  Document,
  Monitor,
  Loading
} from '@element-plus/icons-vue'

// Components
import StatsCard from '@/components/dashboard/StatsCard.vue'
import RestoreTrendChart from '@/components/dashboard/RestoreTrendChart.vue'
import ActivityLog from '@/components/dashboard/ActivityLog.vue'
import SystemStatus from '@/components/dashboard/SystemStatus.vue'

// Composables
import { useDashboard } from '@/composables/useDashboard'
import { healthCheckService } from '@/api/database'

const router = useRouter()

// Dashboard data
const {
  loading,
  error,
  dashboardStats,
  chartData,
  activityLogs,
  serverStatus,
  dbStatus,
  refreshData
} = useDashboard()

// Local state
const showSettings = ref(false)
const showNewRestoreDialog = ref(false)
const showNewSetupDialog = ref(false)
const lastCheckTime = ref<Date>(new Date())

// Computed properties
const successRate = computed(() => {
  if (dashboardStats.value.totalRestores === 0) return 0
  return Math.round((dashboardStats.value.successfulRestores / dashboardStats.value.totalRestores) * 100)
})

// Methods
async function checkSystemHealth() {
  try {
    lastCheckTime.value = new Date()
    // The health check is handled by the dashboard composable
    await refreshData()
    ElMessage.success('System health check completed')
  } catch (err) {
    ElMessage.error('Failed to check system health')
  }
}

function navigateToLogs() {
  // Navigate to logs page (to be implemented)
  ElMessage.info('Logs page - Coming soon')
}

function navigateToMonitoring() {
  // Navigate to monitoring page (to be implemented)
  ElMessage.info('Monitoring page - Coming soon')
}
</script>

<style scoped>
.dashboard-page {
  min-height: 100vh;
}

/* Custom button styles for quick actions */
.el-button.w-full {
  justify-content: flex-start;
}

/* Smooth transitions */
.grid > * {
  transition: all 0.2s ease-in-out;
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Custom scrollbar for activity log */
:deep(.overflow-y-auto::-webkit-scrollbar) {
  width: 4px;
}

:deep(.overflow-y-auto::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 2px;
}

:deep(.overflow-y-auto::-webkit-scrollbar-thumb) {
  background: #cbd5e1;
  border-radius: 2px;
}

:deep(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
  background: #94a3b8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-page .p-6 {
    padding: 1rem;
  }

  .grid.gap-6 {
    gap: 1rem;
  }
}
</style>