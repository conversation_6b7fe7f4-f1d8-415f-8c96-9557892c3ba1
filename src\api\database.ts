import api from './axios'
import type { 
  ApiResponse, 
  ToolRestoreSQLDatabase, 
  SetupToolRestoreSQL 
} from '@/types/database.types'

/**
 * Health Check APIs
 */
export const healthCheckService = {
  // <PERSON><PERSON>m tra trạng thái server
  async ping(): Promise<boolean> {
    try {
      const response = await api.get<ApiResponse>('/ping')
      return response.data.code === 200
    } catch (error) {
      console.error('Ping failed:', error)
      return false
    }
  },

  // Kiểm tra kết nối database
  async pingSQL(): Promise<boolean> {
    try {
      const response = await api.get<ApiResponse>('/ping-sql')
      return response.data.code === 200
    } catch (error) {
      console.error('SQL ping failed:', error)
      return false
    }
  }
}

/**
 * Tool Restore SQL Database APIs
 */
export const restoreDatabaseService = {
  // Lấy danh sách các lần restore database
  async getRestoreList(): Promise<ToolRestoreSQLDatabase[]> {
    try {
      const response = await api.get<ApiResponse<ToolRestoreSQLDatabase[]>>('/GetRestoreSQLDatabase')
      if (response.data.code === 200 && response.data.data) {
        return response.data.data
      }
      throw new Error(response.data.message)
    } catch (error) {
      console.error('Get restore list failed:', error)
      throw error
    }
  },

  // Thêm record restore database mới
  async insertRestore(data: Omit<ToolRestoreSQLDatabase, 'ID' | 'DateTimeRestore'>): Promise<string> {
    try {
      const response = await api.post<ApiResponse<string>>('/InsertRestoreSQLDatabase', data)
      if (response.data.code === 200 && response.data.data) {
        return response.data.data // Returns the new record ID
      }
      throw new Error(response.data.message)
    } catch (error) {
      console.error('Insert restore failed:', error)
      throw error
    }
  }
}

/**
 * Setup Tool Restore SQL APIs
 */
export const setupRestoreService = {
  // Lấy danh sách cấu hình restore database
  async getSetupList(): Promise<SetupToolRestoreSQL[]> {
    try {
      const response = await api.get<ApiResponse<SetupToolRestoreSQL[]>>('/GetSetupToolRestoreSQL')
      if (response.data.code === 200 && response.data.data) {
        return response.data.data
      }
      throw new Error(response.data.message)
    } catch (error) {
      console.error('Get setup list failed:', error)
      throw error
    }
  },

  // Thêm cấu hình restore database mới
  async insertSetup(data: Omit<SetupToolRestoreSQL, 'ID' | 'DateTimeRestore'>): Promise<string> {
    try {
      const response = await api.post<ApiResponse<string>>('/InsertSetupRestoreSQLDatabase', data)
      if (response.data.code === 200 && response.data.data) {
        return response.data.data // Returns the new record ID
      }
      throw new Error(response.data.message)
    } catch (error) {
      console.error('Insert setup failed:', error)
      throw error
    }
  }
}

/**
 * Combined service for dashboard data
 */
export const dashboardService = {
  // Lấy tất cả dữ liệu cần thiết cho dashboard
  async getDashboardData() {
    try {
      const [restoreList, setupList, serverStatus, dbStatus] = await Promise.allSettled([
        restoreDatabaseService.getRestoreList(),
        setupRestoreService.getSetupList(),
        healthCheckService.ping(),
        healthCheckService.pingSQL()
      ])

      return {
        restoreList: restoreList.status === 'fulfilled' ? restoreList.value : [],
        setupList: setupList.status === 'fulfilled' ? setupList.value : [],
        serverStatus: serverStatus.status === 'fulfilled' ? serverStatus.value : false,
        dbStatus: dbStatus.status === 'fulfilled' ? dbStatus.value : false
      }
    } catch (error) {
      console.error('Get dashboard data failed:', error)
      throw error
    }
  }
}
