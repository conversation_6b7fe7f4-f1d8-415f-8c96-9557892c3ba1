/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActivityLog: typeof import('./src/components/dashboard/ActivityLog.vue')['default']
    AppLayout: typeof import('./src/components/layout/AppLayout.vue')['default']
    CustomHeader: typeof import('./src/components/layout/CustomHeader.vue')['default']
    CustomMain: typeof import('./src/components/layout/CustomMain.vue')['default']
    CustomSidebar: typeof import('./src/components/layout/CustomSidebar.vue')['default']
    DatabaseSizeChart: typeof import('./src/components/dashboard/DatabaseSizeChart.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    LanguageSwitcher: typeof import('./src/components/common/LanguageSwitcher.vue')['default']
    RestoreTrendChart: typeof import('./src/components/dashboard/RestoreTrendChart.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SkeletonLoader: typeof import('./src/components/common/SkeletonLoader.vue')['default']
    StatsCard: typeof import('./src/components/dashboard/StatsCard.vue')['default']
    SystemStatus: typeof import('./src/components/dashboard/SystemStatus.vue')['default']
  }
}
