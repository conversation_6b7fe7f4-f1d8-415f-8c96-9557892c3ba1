import { ref, Ref } from 'vue'

export function useLocalStorage<T>(key: string, defaultValue: T): {
  value: Ref<T>
  setValue: (newValue: T) => void
  removeValue: () => void
} {
  const stored = localStorage.getItem(key)
  const value = ref<T>(stored ? JSON.parse(stored) : defaultValue)

  const setValue = (newValue: T): void => {
    value.value = newValue
    localStorage.setItem(key, JSON.stringify(newValue))
  }

  const removeValue = (): void => {
    localStorage.removeItem(key)
    value.value = defaultValue
  }

  return {
    value,
    setValue,
    removeValue
  } 
} 