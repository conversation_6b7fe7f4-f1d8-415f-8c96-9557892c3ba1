/**
 * Database restore types based on API documentation
 */

// Common API Response interface
export interface ApiResponse<T = any> {
  code: number;
  data: T | null;
  message: string;
}

// Tool Restore SQL Database interface
export interface ToolRestoreSQLDatabase {
  ID?: string;              // UUID của record (optional for POST requests)
  IP: string;               // IP address
  DatabaseName: string;     // Tên database
  SizeFileZip: string;      // Kích thước file zip
  SizeBAK: string;          // Kích thước file BAK
  DateTimeRestore?: string; // Ngày restore (format: YYYY-MM-DD) - Auto-generated
  IsSuccess: string;        // Trạng thái thành công ("1" hoặc "0")
  LogContent: string;       // Nội dung log
}

// Setup Tool Restore SQL interface
export interface SetupToolRestoreSQL {
  ID?: string;                 // UUID của record (optional for POST requests)
  IP: string;                  // IP address
  DatabaseName: string;        // Tên database
  ServerSQL: string;           // SQL Server name
  UserSQL: string;             // SQL username
  PasswordSQL: string;         // SQL password
  Destination_folder: string;  // Th<PERSON> mục đích
  Source_File: string;         // File nguồn
  Folder_Logical: string;      // Thư mục logical
  DateTimeRestore?: string;    // Ngày tạo setup (format: YYYY-MM-DD) - Auto-generated
  IsAuto: string;             // Tự động ("1" hoặc "0")
}

// Dashboard statistics interface
export interface DashboardStats {
  totalRestores: number;
  successfulRestores: number;
  failedRestores: number;
  totalSetups: number;
  autoSetups: number;
  recentActivity: number;
}

// Chart data interfaces
export interface ChartDataPoint {
  date: string;
  success: number;
  failed: number;
}

export interface DatabaseSizeData {
  name: string;
  zipSize: number;
  bakSize: number;
}

// Filter interfaces for dashboard
export interface RestoreFilter {
  dateRange?: [string, string];
  ip?: string;
  databaseName?: string;
  status?: 'all' | 'success' | 'failed';
}

export interface SetupFilter {
  dateRange?: [string, string];
  ip?: string;
  databaseName?: string;
  isAuto?: 'all' | 'auto' | 'manual';
}

// Table pagination interface
export interface TablePagination {
  currentPage: number;
  pageSize: number;
  total: number;
}

// Activity log interface for dashboard
export interface ActivityLog {
  id: string;
  type: 'restore' | 'setup';
  action: string;
  ip: string;
  databaseName: string;
  status: 'success' | 'failed' | 'pending';
  timestamp: string;
  details?: string;
}
