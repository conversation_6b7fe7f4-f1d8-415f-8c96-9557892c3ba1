<template>
  <el-dropdown @command="handleLanguageChange" trigger="click">
    <el-button type="primary" plain>
      🌐 {{ currentLanguageName }}
      <el-icon class="el-icon--right">
        <arrow-down />
      </el-icon>
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="vi">
          🇻🇳 {{ t('mes.vietnamese') }}
        </el-dropdown-item>
        <el-dropdown-item command="zh">
          🇨🇳 {{ t('mes.chinese') }}
        </el-dropdown-item>
        <el-dropdown-item command="en">
          🇺🇸 {{ t('mes.english') }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ArrowDown } from '@element-plus/icons-vue'

const { locale, t } = useI18n()

const currentLanguageName = computed(() => {
  const languageMap: Record<string, string> = {
    'vi': t('mes.vietnamese'),
    'zh': t('mes.chinese'),
    'en': t('mes.english')
  }
  return languageMap[locale.value] || t('mes.vietnamese')
})

const handleLanguageChange = (command: string) => {
  locale.value = command
  localStorage.setItem('locale', command)
  // Reload page to apply language change
  window.location.reload()
}
</script>

<style scoped>
.flag {
  margin-right: 8px;
  font-size: 16px;
}

.lang-name {
  font-size: 14px;
}

.is-active {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}
</style> 