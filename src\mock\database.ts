import { faker } from "@faker-js/faker";

export interface DatabaseInfo {
  name: string;
  size: number;
  tables: number;
  lastBackup: string;
  status: "active" | "inactive";
}

export interface TableInfo {
  name: string;
  rows: number;
  size: number;
  lastUpdated: string;
}

export interface DatabaseDetails {
  name: string;
  size: number;
  created: string;
  tables: TableInfo[];
  status: {
    isActive: boolean;
    connections: number;
    lastQuery: string;
  };
  performance: {
    queryCount: number;
    avgResponseTime: number;
    slowQueries: number;
  };
  backups: Array<{
    id: string;
    timestamp: string;
    size: number;
    status: "completed" | "failed";
  }>;
}

// Generate random tables
function generateTables(count: number): TableInfo[] {
  return Array.from({ length: count }, () => ({
    name: faker.database.column().toLowerCase(),
    rows: faker.number.int({ min: 1000, max: 1000000 }),
    size: faker.number.int({ min: 1024 * 1024, max: 1024 * 1024 * 1024 }),
    lastUpdated: faker.date.recent().toISOString(),
  }));
}

// Generate random backups
function generateBackups(
  count: number,
  dbSize: number,
): DatabaseDetails["backups"] {
  return Array.from({ length: count }, () => ({
    id: faker.string.uuid(),
    timestamp: faker.date.recent().toISOString(),
    size: Math.floor(dbSize * faker.number.float({ min: 0.8, max: 1.1 })),
    status: faker.helpers.arrayElement(["completed", "failed"] as const),
  }));
}

// Generate sample databases
export const sampleDatabases: DatabaseInfo[] = Array.from(
  { length: 25 },
  () => {
    const tables = faker.number.int({ min: 5, max: 50 });
    return {
      name: faker.database.collation().toLowerCase().split("_")[0],
      size: faker.number.int({
        min: 1024 * 1024 * 100,
        max: 1024 * 1024 * 1024 * 10,
      }),
      tables,
      lastBackup: faker.date.recent().toISOString(),
      status: faker.helpers.arrayElement(["active", "inactive"] as const),
    };
  },
);

// Get database details by name
export function getDatabaseDetails(name: string): DatabaseDetails {
  const db =
    sampleDatabases.find((db) => db.name === name) || sampleDatabases[0];
  const tables = generateTables(db.tables);

  return {
    name: db.name,
    size: db.size,
    created: faker.date.past().toISOString(),
    tables,
    status: {
      isActive: db.status === "active",
      connections: faker.number.int({ min: 0, max: 100 }),
      lastQuery: faker.date.recent().toISOString(),
    },
    performance: {
      queryCount: faker.number.int({ min: 10000, max: 1000000 }),
      avgResponseTime: faker.number.float({
        min: 10,
        max: 200,
        precision: 0.1,
      }),
      slowQueries: faker.number.int({ min: 0, max: 100 }),
    },
    backups: generateBackups(5, db.size),
  };
}

// Get performance data for charts
export function getPerformanceData() {
  const timePoints = ["00:00", "04:00", "08:00", "12:00", "16:00", "20:00"];

  return {
    times: timePoints,
    queries: timePoints.map(() => faker.number.int({ min: 100, max: 500 })),
    responseTime: timePoints.map(() =>
      faker.number.float({ min: 10, max: 50, precision: 0.1 }),
    ),
    memory: timePoints.map(() =>
      faker.number.float({ min: 1, max: 8, precision: 0.1 }),
    ),
  };
}

// Simulate API pagination
export function getPaginatedDatabases(
  page: number,
  limit: number,
  search: string = "",
) {
  const filtered = sampleDatabases.filter((db) =>
    search ? db.name.includes(search.toLowerCase()) : true,
  );

  const start = (page - 1) * limit;
  const end = start + limit;

  return {
    items: filtered.slice(start, end),
    total: filtered.length,
  };
}
