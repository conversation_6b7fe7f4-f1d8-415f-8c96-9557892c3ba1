<template>
  <div class="stats-card bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center',
              iconBgClass
            ]">
              <el-icon :size="24" :class="iconClass">
                <component :is="icon" />
              </el-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">{{ title }}</p>
            <p class="text-2xl font-bold text-gray-900">{{ formattedValue }}</p>
          </div>
        </div>
        <div v-if="subtitle" class="mt-2">
          <p class="text-sm text-gray-500">{{ subtitle }}</p>
        </div>
      </div>
      <div v-if="trend" class="flex-shrink-0">
        <div :class="[
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          trend.type === 'increase' ? 'bg-green-100 text-green-800' : 
          trend.type === 'decrease' ? 'bg-red-100 text-red-800' : 
          'bg-gray-100 text-gray-800'
        ]">
          <el-icon v-if="trend.type === 'increase'" class="mr-1" :size="12">
            <ArrowUp />
          </el-icon>
          <el-icon v-else-if="trend.type === 'decrease'" class="mr-1" :size="12">
            <ArrowDown />
          </el-icon>
          {{ trend.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

interface Trend {
  type: 'increase' | 'decrease' | 'neutral'
  value: string
}

interface Props {
  title: string
  value: number | string
  subtitle?: string
  icon: any
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'gray'
  trend?: Trend
}

const props = withDefaults(defineProps<Props>(), {
  color: 'blue'
})

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString()
  }
  return props.value
})

const iconBgClass = computed(() => {
  const colorMap = {
    blue: 'bg-blue-100',
    green: 'bg-green-100',
    red: 'bg-red-100',
    yellow: 'bg-yellow-100',
    purple: 'bg-purple-100',
    gray: 'bg-gray-100'
  }
  return colorMap[props.color]
})

const iconClass = computed(() => {
  const colorMap = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    red: 'text-red-600',
    yellow: 'text-yellow-600',
    purple: 'text-purple-600',
    gray: 'text-gray-600'
  }
  return colorMap[props.color]
})
</script>

<style scoped>
.stats-card {
  transition: all 0.2s ease-in-out;
}

.stats-card:hover {
  transform: translateY(-1px);
}
</style>
