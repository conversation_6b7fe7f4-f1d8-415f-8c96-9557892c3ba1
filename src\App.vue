<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { useAppStore } from "@/stores/app";
import { useAuthStore } from "@/stores/auth";

const appStore = useAppStore();
const authStore = useAuthStore();

onMounted(() => {
  // Initialize theme
  authStore.initializeAuth();
  appStore.initTheme();
});
</script>

<style>
#app {
  height: 100vh;
  font-family:
    "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

/* Global styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

/* Dark mode global styles */
.dark {
  color-scheme: dark;
}

.dark body {
  background-color: #141414;
  color: #e5eaf3;
}

.dark,
.dark body,
.dark #app {
  --el-text-color-primary: #fff;
  --el-text-color-regular: #fff;
  --el-text-color-secondary: #fff;
  --el-text-color-placeholder: #bbb;
  --el-bg-color: #181818;
  --el-bg-color-page: #181818;
  --el-bg-color-overlay: #232323;
}
</style>
