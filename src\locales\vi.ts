export default {
  lagname: "vi",
  mes: {
    // Common actions
    add: "Thêm",
    edit: "Sửa",
    delete: "<PERSON><PERSON><PERSON>",
    save: "<PERSON><PERSON><PERSON>",
    cancel: "<PERSON><PERSON><PERSON>",
    confirm: "<PERSON><PERSON><PERSON> nhận",
    search: "<PERSON><PERSON><PERSON> kiếm",
    refresh: "<PERSON><PERSON><PERSON> mới",
    create: "<PERSON>ạo mới",
    update: "Cập nhật",
    close: "Đóng",
    back: "Quay lại",
    actions: "<PERSON><PERSON> tác",
    detail: "Chi tiết",
    
    // Navigation
    dashboard: "Bảng điều khiển",
    products: "Sản phẩm",
    orders: "Đơn hàng",
    materials: "Nguyên vật liệu",
    bom: "BOM",
    homepage: "Trang chủ",
    settings: "Cài đặt",
    
    // User
    user: "Người dùng",
    profile: "<PERSON><PERSON> sơ",
    logout: "Đăng xuất",
    login: "<PERSON><PERSON><PERSON> nhập",
    username: "<PERSON><PERSON><PERSON> đăng nhập",
    password: "<PERSON><PERSON><PERSON> khẩu",
    systemDescription: "<PERSON><PERSON> thống quản lý sản xuất",
    pleaseEnterUsername: "<PERSON><PERSON> lòng nhập tên đăng nhập",
    pleaseEnterPassword: "Vui lòng nhập mật khẩu",
    
    // Status
    loading: "Đang tải...",
    success: "Thành công",
    error: "Lỗi",
    warning: "Cảnh báo",
    info: "Thông tin",
    pending: "Chờ xử lý",
    inProgress: "Đang xử lý",
    completed: "Hoàn thành",
    cancelled: "Đã hủy",
    
    // Table
    noData: "Không có dữ liệu",
    total: "Tổng cộng",
    items: "mục",
    page: "Trang",
    of: "của",
    
    // Form labels
    name: "Tên",
    code: "Mã",
    description: "Mô tả",
    price: "Giá",
    quantity: "Số lượng",
    date: "Ngày",
    status: "Trạng thái",
    color: "Màu sắc",
    unit: "Đơn vị",
    supplier: "Nhà cung cấp",
    cost: "Chi phí",
    weight: "Trọng lượng (g)",
    injectionTime: "Thời gian ép (s)",
    note: "Ghi chú",
    unitPrice: "Đơn giá",
    totalAmount: "Thành tiền",
    product: "Sản phẩm",
    
    // Stock management
    addStock: "Nhập kho",
    reduceStock: "Xuất kho",
    stockQuantity: "Số lượng tồn kho",
    stock: "Tồn kho",
    noBOMForStock: "Không thể nhập kho: Sản phẩm chưa có BOM",
    
    // Messages
    confirmDelete: "Bạn có chắc chắn muốn xóa?",
    deleteSuccess: "Xóa thành công",
    saveSuccess: "Lưu thành công",
    updateSuccess: "Cập nhật thành công",
    createSuccess: "Tạo mới thành công",
    pleaseSelect: "Vui lòng chọn",
    pleaseEnter: "Vui lòng nhập",
    
    // Page titles
    productManagement: "Quản lý sản phẩm",
    orderManagement: "Quản lý đơn hàng",
    materialManagement: "Quản lý nguyên vật liệu",
    bomManagement: "Quản lý BOM",
    
    // Actions
    addProduct: "Thêm sản phẩm",
    addOrder: "Tạo đơn hàng",
    addMaterial: "Thêm nguyên vật liệu",
    addBOM: "Thêm BOM",
    calculateCost: "Tính chi phí",
    selectProduct: "Chọn sản phẩm",
    
    // Filters
    all: "Tất cả",
    filterByColor: "Lọc theo màu",
    filterByStatus: "Lọc theo trạng thái",
    searchProduct: "Tìm kiếm sản phẩm...",
    searchOrder: "Tìm kiếm đơn hàng...",
    searchMaterial: "Tìm kiếm vật liệu...",
    
    // Statistics
    totalOrders: "Tổng đơn hàng",
    totalProducts: "Sản phẩm",
    totalMaterials: "Nguyên vật liệu",
    pendingOrders: "Đơn hàng chờ",
    lowStockAlert: "Cảnh báo tồn kho",
    recentOrders: "Đơn hàng gần đây",
    remaining: "Còn lại",
    noLowStockAlert: "Không có cảnh báo tồn kho",
    noRecentOrders: "Không có đơn hàng gần đây",
    topSellingProducts: "Top sản phẩm bán chạy",
    orderStatisticsByMonth: "Thống kê đơn hàng theo tháng",
    month: "Tháng",
    
    // Inventory Statistics
    totalInventoryValue: "Tổng giá trị tồn kho",
    averageQuantity: "Số lượng trung bình",
    lowStockProducts: "Sản phẩm tồn kho thấp",
    outOfStockProducts: "Sản phẩm hết hàng",
    lowStockAlerts: "Cảnh báo tồn kho thấp",
    noLowStockProducts: "Không có sản phẩm tồn kho thấp",
    topInventoryProducts: "Top sản phẩm tồn kho cao",
    inventoryMovements: "Chuyển động kho",
    stockIn: "Nhập kho",
    stockOut: "Xuất kho",
    stockAdjust: "Điều chỉnh kho",
    movements: "Chuyển động",
    
    // Movement History
    movementHistory: "Lịch sử kho",
    movementHistoryDescription: "Theo dõi và quản lý lịch sử chuyển động kho chi tiết",
    movementType: "Loại chuyển động",
    selectMovementType: "Chọn loại chuyển động",
    dateRange: "Khoảng thời gian",
    startDate: "Ngày bắt đầu",
    endDate: "Ngày kết thúc",
    searchByNotes: "Tìm kiếm theo ghi chú",
    enterKeyword: "Nhập từ khóa...",
    pleaseEnterKeyword: "Vui lòng nhập từ khóa",
    orderSummary: "Báo cáo theo đơn hàng",
    charts: "Biểu đồ",
    movementTypeChart: "Biểu đồ loại chuyển động",
    movementValueChart: "Biểu đồ giá trị chuyển động",
    totalIn: "Tổng nhập",
    totalOut: "Tổng xuất",
    netMovement: "Chuyển động ròng",
    movementCount: "Số lần chuyển động",
    lastMovement: "Chuyển động cuối",
    totalQuantity: "Tổng số lượng",
    totalValue: "Tổng giá trị",
    cannotLoadMovementHistory: "Không thể tải lịch sử chuyển động",
    cannotLoadMovementStats: "Không thể tải thống kê chuyển động",
    cannotLoadOrderSummary: "Không thể tải báo cáo đơn hàng",
    cannotSearchNotes: "Không thể tìm kiếm ghi chú",
    reset: "Đặt lại",
    
    // Order specific
    orderCode: "Mã đơn hàng",
    orderDate: "Ngày đặt",
    planDeliveryDate: "Ngày giao dự kiến",
    actualDeliveryDate: "Ngày giao thực tế",
    productCount: "Số lượng SP",
    createdAt: "Ngày tạo",
    orderDetail: "Chi tiết đơn hàng",
    productList: "Danh sách sản phẩm",
    productCode: "Mã SP",
    shippedQuantity: "Đã giao",
    createNewOrder: "Tạo đơn hàng mới",
    updateOrder: "Cập nhật đơn hàng",
    editOrder: "Sửa đơn hàng",
    deleteOrder: "Xóa đơn hàng",
    selectOrderDate: "Chọn ngày đặt hàng",
    selectPlanDeliveryDate: "Chọn ngày giao dự kiến",
    selectActualDeliveryDate: "Chọn ngày giao thực tế",
    selectStatus: "Chọn trạng thái",
    currentProductList: "Danh sách sản phẩm hiện tại",
    addNewProducts: "Thêm sản phẩm mới",
    noNewProducts: "Chưa có sản phẩm mới",
    pleaseSelectOrderDate: "Vui lòng chọn ngày đặt hàng",
    pleaseSelectPlanDeliveryDate: "Vui lòng chọn ngày giao dự kiến",
    pleaseSelectStatus: "Vui lòng chọn trạng thái",
    cannotLoadProducts: "Không thể tải danh sách sản phẩm",
    updateOrderSuccess: "Cập nhật đơn hàng thành công",
    cannotUpdateOrder: "Không thể cập nhật đơn hàng",
    
    // Material specific
    materialCode: "Mã VL",
    
    // 404 Page
    pageNotFound: "Trang không tồn tại",
    pageNotFoundDescription: "Xin lỗi, trang bạn đang tìm kiếm không tồn tại hoặc đã được di chuyển.",
    backToHome: "Về trang chủ",
    goBack: "Quay lại",
    helpfulLinks: "Liên kết hữu ích",
    materialName: "Tên vật liệu",
    noMaterials: "Không có vật liệu nào",
    cannotLoadMaterials: "Không thể tải danh sách vật liệu",
    addNewMaterial: "Thêm vật liệu mới",
    editMaterial: "Sửa vật liệu",
    deleteMaterial: "Xóa vật liệu",
    pleaseEnterMaterialCode: "Vui lòng nhập mã vật liệu",
    pleaseEnterMaterialName: "Vui lòng nhập tên vật liệu",
    pleaseEnterSupplier: "Vui lòng nhập nhà cung cấp",
    pleaseEnterUnit: "Vui lòng nhập đơn vị",
    addMaterialSuccess: "Thêm vật liệu thành công",
    cannotAddMaterial: "Không thể thêm vật liệu",
    
    // Material form validation messages
    materialCodeLength: "Mã vật liệu phải từ 2-50 ký tự",
    materialNameLength: "Tên vật liệu phải từ 2-100 ký tự",
    supplierLength: "Tên nhà cung cấp phải từ 2-100 ký tự",
    unitLength: "Đơn vị phải từ 1-20 ký tự",
    unitCostMustBePositive: "Đơn giá phải lớn hơn 0",
    
    // Material form placeholders
    enterMaterialCode: "Nhập mã vật liệu...",
    enterMaterialName: "Nhập tên vật liệu...",
    enterSupplier: "Nhập tên nhà cung cấp...",
    enterUnitCost: "Nhập đơn giá",
    enterUnit: "Nhập đơn vị...",
    selectSupplier: "Chọn nhà cung cấp",
    newMaterial: "Vật liệu mới",
    addingMaterial: "Đang thêm...",
    
    // BOM specific
    bomForProduct: "BOM của sản phẩm",
    costCalculationResult: "Kết quả tính chi phí",
    totalCost: "Tổng chi phí",
    addBOMEntries: "Thêm BOM entries",
    selectMaterial: "Chọn vật liệu",
    pleaseSelectMaterial: "Vui lòng chọn vật liệu",
    addBOMSuccess: "Thêm BOM thành công",
    cannotAddBOM: "Không thể thêm BOM",

    // Product specific
    productName: "Tên sản phẩm",
    addNewProduct: "Thêm sản phẩm mới",
    editProduct: "Sửa sản phẩm",
    deleteProduct: "Xóa sản phẩm",
    pleaseEnterProductName: "Vui lòng nhập tên sản phẩm",
    pleaseEnterWeight: "Vui lòng nhập trọng lượng",
    pleaseEnterInjectionTime: "Vui lòng nhập thời gian ép",
    pleaseEnterColor: "Vui lòng nhập màu sắc",
    pleaseEnterUnitPrice: "Vui lòng nhập đơn giá",
    pleaseEnterQuantity: "Vui lòng nhập số lượng",
    addProductSuccess: "Thêm sản phẩm thành công",
    cannotAddProduct: "Không thể thêm sản phẩm",
    addStockSuccess: "Nhập kho thành công",
    reduceStockSuccess: "Xuất kho thành công",
    cannotAddStock: "Không thể nhập kho",
    cannotReduceStock: "Không thể xuất kho",
    
    // Form validation messages
    productNameLength: "Tên sản phẩm phải từ 2-100 ký tự",
    weightMustBePositive: "Trọng lượng phải lớn hơn 0",
    injectionTimeMustBePositive: "Thời gian ép phải lớn hơn 0",
    unitPriceMustBePositive: "Đơn giá phải lớn hơn 0",
    pleaseCheckFormErrors: "Vui lòng kiểm tra lỗi trong form",
    
    // Form placeholders and labels
    enterProductName: "Nhập tên sản phẩm...",
    enterWeight: "Nhập trọng lượng",
    enterInjectionTime: "Nhập thời gian ép",
    enterUnitPrice: "Nhập đơn giá",
    enterDescription: "Nhập mô tả sản phẩm...",
    selectColor: "Chọn màu sắc",
    additionalInfo: "Thông tin bổ sung",
    newProduct: "Sản phẩm mới",
    adding: "Đang thêm...",
    allFieldsRequired: "Tất cả các trường đều bắt buộc",
    
    // Color options
    white: "Trắng",
    black: "Đen", 
    red: "Đỏ",
    blue: "Xanh dương",
    green: "Xanh lá",
    yellow: "Vàng",
    gray: "Xám",
    
    // Units
    seconds: "giây",
    
    // Form state messages
    unsavedChanges: "Thay đổi chưa lưu",
    unsavedChangesMessage: "Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn hủy?",
    keepEditing: "Tiếp tục chỉnh sửa",
    discardChanges: "Hủy thay đổi",
    restorePreviousData: "Bạn có dữ liệu đã lưu trước đó. Bạn có muốn khôi phục không?",
    savedDataFound: "Dữ liệu đã lưu",
    restore: "Khôi phục",
    startFresh: "Bắt đầu mới",

    // Dashboard
    welcome: "Chào mừng đến với hệ thống ERP",
    recentProducts: "Sản phẩm gần đây",
    recentMaterials: "Vật liệu gần đây",
    viewAll: "Xem tất cả",

    // Language
    language: "Ngôn ngữ",
    vietnamese: "Tiếng Việt",
    chinese: "中文",
    english: "English",

    // Currency
    currency: "Tiền tệ",
    vnd: "Đồng Việt Nam",
    cny: "Nhân dân tệ",
    usd: "Đô la Mỹ",
    currencySymbol: "₫",
    currencyFormat: "VND",

    // New keys for BOM page
    loadProductError: 'Không thể tải danh sách sản phẩm',
    loadMaterialError: 'Không thể tải danh sách vật liệu',
    loadBOMError: 'Không thể tải BOM',
    calculateCostError: 'Không thể tính chi phí',
    calculateCostSuccess: 'Tính chi phí thành công',
    quantityMustBePositive: 'Số lượng phải lớn hơn 0',
    pleaseSelectOrderStatus: 'Vui lòng chọn trạng thái',
    loadOrderError: 'Không thể tải danh sách đơn hàng',
    confirmDeleteProduct: 'Bạn có chắc chắn muốn xóa sản phẩm "{name}"?',
    deleteProductSuccess: 'Xóa sản phẩm thành công',
    cannotDeleteProduct: 'Không thể xóa sản phẩm',
    updateBOM: 'Cập nhật BOM',
    updateBOMSuccess: 'Cập nhật BOM thành công',
    cannotUpdateBOM: 'Không thể cập nhật BOM',
    updateQuantity: 'Cập nhật số lượng',
    updateQuantitySuccess: 'Cập nhật số lượng vật tư thành công',
    cannotUpdateQuantity: 'Không thể cập nhật số lượng vật tư',
    selectOrder: "Chọn đơn hàng"
  }
}; 