<!doctype html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/src/assets/logo.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="IT Supreme Leader" />
    <meta name="keywords" content="IT Supreme Leader" />
    <meta name="author" content="IT Supreme Leader" />
    <title>IT Supreme Leader</title>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
<style>
  html,
  body {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    padding: 0px;
    margin: 0px;
  }
  html {
    font-size: 18px;
  }
  body {
    font-size: 1rem;
  }
  /* (600px - 900px) */
  @media only screen and (min-width: 600px) and (max-width: 900px) {
    html {
      font-size: 16px;
    }
  }
  /* (600px) */
  @media only screen and (max-width: 599px) {
    html {
      font-size: 14px;
    }
  }
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }
</style>
