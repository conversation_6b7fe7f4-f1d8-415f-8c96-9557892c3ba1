<template>
  <el-header class="header">
    <div class="header-left">
      <el-button text @click="toggleSidebar" class="sidebar-toggle">
        <el-icon size="20">
          <Fold v-if="!sidebarCollapsed" />
          <Expand v-else />
        </el-icon>
      </el-button>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">{{
          t("mes.dashboard")
        }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ pageTitle }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="header-right">
      <el-button text @click="toggleDarkMode" class="theme-toggle">
        <el-icon size="20">
          <Moon v-if="!isDarkMode" />
          <Sunny v-else />
        </el-icon>
      </el-button>

      <LanguageSwitcher />

      <el-dropdown @command="handleUserCommand" trigger="click">
        <span class="user-dropdown">
          <el-avatar  :src="`https://bpm.tyxuan.com.vn/UOF/HinhNV/${user}.JPG`" /> <span
            class="username">{{ user || t("mes.user") }}</span>
          <el-icon>
            <ArrowDown />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">{{
              t("mes.profile")
            }}</el-dropdown-item>
            <el-dropdown-item command="logout" divided>{{
              t("mes.logout")
            }}</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-header>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { useAuthStore } from "@/stores/auth";
import { useAppStore } from "@/stores/app";
import LanguageSwitcher from "@/components/common/LanguageSwitcher.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";


const router = useRouter();

const route = useRoute();
const { t } = useI18n();
const authStore = useAuthStore();
const appStore = useAppStore();

const user = computed(() => authStore.user?.userID);
const isDarkMode = computed(() => appStore.isDarkMode);
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed);

const pageTitle = computed(() => {
  const i18nKey = (route.meta.title as string) || "mes.dashboard";
  return t(i18nKey);
});

const toggleSidebar = () => {
  appStore.toggleSidebar();
};

const toggleDarkMode = () => {
  appStore.toggleDarkMode();
};

const handleUserCommand = async (command: string) => {
  if (command === "logout") {
    try {
      await ElMessageBox.confirm(
        "Bạn có chắc chắn muốn đăng xuất?",
        "Xác nhận đăng xuất",
        {
          confirmButtonText: "Đăng xuất",
          cancelButtonText: "Hủy",
          type: "warning",
        }
      );

      // Thực hiện logout
      authStore.logout();

      // Hiển thị thông báo thành công
      ElMessage.success("Đăng xuất thành công!");

      // Chuyển hướng về trang login
      await router.push("/login");
    } catch (error) {
      // User cancelled logout
      console.log("Logout cancelled");
    }
  }
};

</script>

<style scoped>
.header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background-color: var(--el-bg-color-overlay);
  border-bottom: 1px solid var(--el-border-color);
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  color: var(--el-text-color-primary);
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}
</style>
