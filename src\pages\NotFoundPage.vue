<template>
  <div class="not-found-page">
    <el-container class="not-found-container">
      <el-main class="not-found-main">
        <div class="not-found-content">
          <!-- Error Code Section -->
          <div class="error-section">
            <div class="error-code">404</div>
            <div class="error-divider"></div>
          </div>
          
          <!-- Error Information -->
          <div class="error-info">
            <el-icon class="error-icon" size="64">
              <WarningFilled />
            </el-icon>
            <h1 class="error-title">{{ t('mes.pageNotFound') }}</h1>
            <p class="error-description">
              {{ t('mes.pageNotFoundDescription') }}
            </p>
          </div>
          
          <!-- Action Buttons -->
          <div class="error-actions">
            <el-button 
              type="primary" 
              size="large" 
              @click="goHome"
              class="primary-action"
            >
              <el-icon><House /></el-icon>
              {{ t('mes.backToHome') }}
            </el-button>
            
            <el-button 
              size="large" 
              @click="goBack"
              class="secondary-action"
            >
              <el-icon><ArrowLeft /></el-icon>
              {{ t('mes.goBack') }}
            </el-button>
          </div>
          
          <!-- Helpful Links -->
          <div class="helpful-links">
            <p class="links-title">{{ t('mes.helpfulLinks') }}</p>
            <div class="links-grid">
              <el-link 
                type="primary" 
                @click="goToDashboard"
                class="helpful-link"
              >
                <el-icon><DataBoard /></el-icon>
                {{ t('mes.dashboard') }}
              </el-link>
              
              <el-link 
                type="primary" 
                @click="goToProducts"
                class="helpful-link"
              >
                <el-icon><Goods /></el-icon>
                {{ t('mes.products') }}
              </el-link>
              
              <el-link 
                type="primary" 
                @click="goToOrders"
                class="helpful-link"
              >
                <el-icon><Document /></el-icon>
                {{ t('mes.orders') }}
              </el-link>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { 
  House, 
  ArrowLeft, 
  WarningFilled,
  DataBoard,
  Goods,
  Document
} from '@element-plus/icons-vue'

const router = useRouter()
const { t } = useI18n()

// Primary action - go to home
const goHome = () => {
  router.push('/dashboard')
}

// Secondary action - go back
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/dashboard')
  }
}

// Helpful navigation links
const goToDashboard = () => {
  router.push('/dashboard')
}

const goToProducts = () => {
  router.push('/products')
}

const goToOrders = () => {
  router.push('/orders')
}
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
  background: var(--el-bg-color-page);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.not-found-container {
  max-width: 600px;
  width: 100%;
  background: var(--el-bg-color);
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.not-found-main {
  padding: 60px 40px;
  text-align: center;
}

.not-found-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
}

/* Error Section */
.error-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.error-code {
  font-size: 72px;
  font-weight: 700;
  color: var(--el-color-primary);
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error-divider {
  width: 4px;
  height: 72px;
  background: var(--el-border-color);
  border-radius: 2px;
}

/* Error Info */
.error-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.error-icon {
  color: var(--el-color-warning);
  margin-bottom: 8px;
}

.error-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
  line-height: 1.2;
}

.error-description {
  font-size: 16px;
  color: var(--el-text-color-regular);
  margin: 0;
  line-height: 1.5;
  max-width: 400px;
}

/* Action Buttons */
.error-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 8px;
}

.primary-action {
  min-width: 140px;
  height: 44px;
  font-weight: 500;
}

.secondary-action {
  min-width: 140px;
  height: 44px;
  font-weight: 500;
}

/* Helpful Links */
.helpful-links {
  margin-top: 16px;
  padding-top: 32px;
  border-top: 1px solid var(--el-border-color-lighter);
  width: 100%;
}

.links-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin: 0 0 16px 0;
  font-weight: 500;
}

.links-grid {
  display: flex;
  gap: 24px;
  justify-content: center;
  flex-wrap: wrap;
}

.helpful-link {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: var(--el-border-radius-base);
  transition: all 0.2s ease;
}

.helpful-link:hover {
  background: var(--el-color-primary-light-9);
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .not-found-main {
    padding: 40px 24px;
  }
  
  .error-code {
    font-size: 56px;
  }
  
  .error-divider {
    height: 56px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .error-description {
    font-size: 14px;
  }
  
  .error-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .primary-action,
  .secondary-action {
    width: 100%;
  }
  
  .links-grid {
    flex-direction: column;
    gap: 12px;
  }
  
  .helpful-link {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .not-found-page {
    padding: 16px;
  }
  
  .not-found-main {
    padding: 32px 20px;
  }
  
  .error-code {
    font-size: 48px;
  }
  
  .error-divider {
    height: 48px;
  }
  
  .error-title {
    font-size: 20px;
  }
  
  .error-description {
    font-size: 13px;
  }
}

/* Dark mode adjustments */
:deep(.dark) .not-found-container {
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-darker);
}

:deep(.dark) .error-code {
  color: var(--el-color-primary-light-3);
}

:deep(.dark) .helpful-link:hover {
  background: var(--el-color-primary-dark-2);
}
</style> 