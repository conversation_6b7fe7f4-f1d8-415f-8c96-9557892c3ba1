<template>
  <div class="chart-container bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Database Sizes (Top 10)</h3>
      <div class="flex items-center space-x-4">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">ZIP Size</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">BAK Size</span>
        </div>
      </div>
    </div>
    <div ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { DatabaseSizeData } from '@/types/database.types'

interface Props {
  data: DatabaseSizeData[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

function initChart() {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

function updateChart() {
  if (!chartInstance || !props.data.length) return

  const databases = props.data.map(item => item.name)
  const zipSizes = props.data.map(item => item.zipSize)
  const bakSizes = props.data.map(item => item.bakSize)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        let tooltip = `<div class="font-semibold">${params[0].name}</div>`
        params.forEach((param: any) => {
          tooltip += `<div class="flex items-center mt-1">
            <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${param.color}"></div>
            <span>${param.seriesName}: ${param.value} MB</span>
          </div>`
        })
        return tooltip
      }
    },
    legend: {
      data: ['ZIP Size', 'BAK Size'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: databases,
      axisLabel: {
        rotate: 45,
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      name: 'Size (MB)',
      axisLabel: {
        formatter: '{value} MB'
      }
    },
    series: [
      {
        name: 'ZIP Size',
        type: 'bar',
        data: zipSizes,
        itemStyle: {
          color: '#3b82f6'
        },
        emphasis: {
          focus: 'series'
        }
      },
      {
        name: 'BAK Size',
        type: 'bar',
        data: bakSizes,
        itemStyle: {
          color: '#22c55e'
        },
        emphasis: {
          focus: 'series'
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

function resizeChart() {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// Watch for data changes
watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// Watch for loading state
watch(() => props.loading, (loading) => {
  if (chartInstance) {
    if (loading) {
      chartInstance.showLoading()
    } else {
      chartInstance.hideLoading()
    }
  }
})

onMounted(() => {
  initChart()
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
.chart-container {
  min-height: 400px;
}
</style>
