<template>
  <div class="system-status bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">System Status</h3>
      <el-button 
        type="primary" 
        size="small" 
        :icon="Refresh" 
        @click="$emit('refresh')"
        :loading="loading"
      >
        Check Status
      </el-button>
    </div>

    <div class="space-y-4">
      <!-- Server Status -->
      <div class="flex items-center justify-between p-4 rounded-lg border border-gray-200">
        <div class="flex items-center">
          <div :class="[
            'w-3 h-3 rounded-full mr-3',
            serverStatus ? 'bg-green-500' : 'bg-red-500'
          ]"></div>
          <div>
            <h4 class="text-sm font-medium text-gray-900">API Server</h4>
            <p class="text-xs text-gray-500">Backend service connectivity</p>
          </div>
        </div>
        <div class="flex items-center">
          <span :class="[
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            serverStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          ]">
            <el-icon :size="12" class="mr-1">
              <component :is="serverStatus ? Check : Close" />
            </el-icon>
            {{ serverStatus ? 'Online' : 'Offline' }}
          </span>
        </div>
      </div>

      <!-- Database Status -->
      <div class="flex items-center justify-between p-4 rounded-lg border border-gray-200">
        <div class="flex items-center">
          <div :class="[
            'w-3 h-3 rounded-full mr-3',
            dbStatus ? 'bg-green-500' : 'bg-red-500'
          ]"></div>
          <div>
            <h4 class="text-sm font-medium text-gray-900">SQL Database</h4>
            <p class="text-xs text-gray-500">Database connectivity</p>
          </div>
        </div>
        <div class="flex items-center">
          <span :class="[
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            dbStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          ]">
            <el-icon :size="12" class="mr-1">
              <component :is="dbStatus ? Check : Close" />
            </el-icon>
            {{ dbStatus ? 'Connected' : 'Disconnected' }}
          </span>
        </div>
      </div>

      <!-- Overall System Health -->
      <div class="mt-6 p-4 rounded-lg" :class="overallHealthClass">
        <div class="flex items-center">
          <el-icon :size="20" :class="overallHealthIconClass" class="mr-3">
            <component :is="overallHealthIcon" />
          </el-icon>
          <div>
            <h4 class="text-sm font-medium" :class="overallHealthTextClass">
              {{ overallHealthTitle }}
            </h4>
            <p class="text-xs" :class="overallHealthSubtextClass">
              {{ overallHealthMessage }}
            </p>
          </div>
        </div>
      </div>

      <!-- Last Check Time -->
      <div v-if="lastCheckTime" class="text-center">
        <p class="text-xs text-gray-500">
          Last checked: {{ formatLastCheck(lastCheckTime) }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Refresh, 
  Check, 
  Close, 
  Warning,
  CircleCheck,
  CircleClose
} from '@element-plus/icons-vue'
import { format } from 'date-fns'

interface Props {
  serverStatus: boolean
  dbStatus: boolean
  loading?: boolean
  lastCheckTime?: Date
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

defineEmits<{
  refresh: []
}>()

const overallHealth = computed(() => {
  if (props.serverStatus && props.dbStatus) {
    return 'healthy'
  } else if (props.serverStatus || props.dbStatus) {
    return 'warning'
  } else {
    return 'critical'
  }
})

const overallHealthClass = computed(() => {
  switch (overallHealth.value) {
    case 'healthy':
      return 'bg-green-50 border border-green-200'
    case 'warning':
      return 'bg-yellow-50 border border-yellow-200'
    case 'critical':
      return 'bg-red-50 border border-red-200'
    default:
      return 'bg-gray-50 border border-gray-200'
  }
})

const overallHealthTextClass = computed(() => {
  switch (overallHealth.value) {
    case 'healthy':
      return 'text-green-800'
    case 'warning':
      return 'text-yellow-800'
    case 'critical':
      return 'text-red-800'
    default:
      return 'text-gray-800'
  }
})

const overallHealthSubtextClass = computed(() => {
  switch (overallHealth.value) {
    case 'healthy':
      return 'text-green-600'
    case 'warning':
      return 'text-yellow-600'
    case 'critical':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
})

const overallHealthIconClass = computed(() => {
  switch (overallHealth.value) {
    case 'healthy':
      return 'text-green-600'
    case 'warning':
      return 'text-yellow-600'
    case 'critical':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
})

const overallHealthIcon = computed(() => {
  switch (overallHealth.value) {
    case 'healthy':
      return CircleCheck
    case 'warning':
      return Warning
    case 'critical':
      return CircleClose
    default:
      return Warning
  }
})

const overallHealthTitle = computed(() => {
  switch (overallHealth.value) {
    case 'healthy':
      return 'System Healthy'
    case 'warning':
      return 'System Warning'
    case 'critical':
      return 'System Critical'
    default:
      return 'System Unknown'
  }
})

const overallHealthMessage = computed(() => {
  if (props.serverStatus && props.dbStatus) {
    return 'All systems are operational'
  } else if (props.serverStatus && !props.dbStatus) {
    return 'Database connection issues detected'
  } else if (!props.serverStatus && props.dbStatus) {
    return 'API server connection issues detected'
  } else {
    return 'Multiple system failures detected'
  }
})

function formatLastCheck(date: Date) {
  return format(date, 'MMM dd, yyyy HH:mm:ss')
}
</script>

<style scoped>
.system-status {
  min-height: 300px;
}
</style>
