import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { login as loginService, logout as logoutService, getCurrentUser, isAuthenticated } from '@/api/auth';
import type { LoginRequest, UserData, AuthState } from '@/types/auth.types';

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<UserData | null>(null);
  const token = ref<string | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Computed
  const isAuthenticated = computed(() => !!user.value && !!token.value);
  const userRole = computed(() => user.value?.role || '');
  const userName = computed(() => user.value?.userName || '');
  const userId = computed(() => user.value?.userId || '');


  // Actions
  async function login(credentials: LoginRequest): Promise<boolean> {
    loading.value = true;
    error.value = null;

    try {
      const userData = await loginService(credentials);

      user.value = userData;
      token.value = userData.token || null;

      return true;
    } catch (err: any) {
      error.value = err.message;
      return false;
    } finally {
      loading.value = false;
    }
  }

  function logout(): void {
    try {
      logoutService();
      user.value = null;
      token.value = null;
      error.value = null;
    } catch (err: any) {
    }
  }

  function initializeAuth(): void {
    try {
      const currentUser = getCurrentUser();
      const authToken = localStorage.getItem('auth-token');
      
      if (currentUser && authToken) {
        user.value = currentUser;
        token.value = authToken;
      } else {
      }
    } catch (err: any) {
      // Clear invalid data
      logout();
    }
  }

  function clearError(): void {
    error.value = null;
  }

  // Get current state
  function getAuthState(): AuthState {
    return {
      isAuthenticated: isAuthenticated.value,
      user: user.value,
      token: token.value,
      loading: loading.value,
      error: error.value
    };
  }

  return {
    // State
    user,
    token,
    loading,
    error,
    
    // Computed
    isAuthenticated,
    userRole,
    userName,
    
    // Actions
    login,
    logout,
    initializeAuth,
    clearError,
    getAuthState
  };
}); 